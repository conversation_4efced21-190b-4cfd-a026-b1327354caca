<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Session;
use App\Models\Giftcode;
use App\Models\GiftcodeLog;
use App\Services\ItemService;
use App\Services\GiftcodeService;

class GiftcodeController extends Controller
{
    protected $giftcodeService;

    public function __construct(GiftcodeService $giftcodeService)
    {
        $this->giftcodeService = $giftcodeService;
    }

    public function index(Request $request)
    {
        $admin = Session::get('admin_user');
        $search = $request->get('search');
        $statusFilter = $request->get('status', 'all');
        $typeFilter = $request->get('type', 'all');

        // Base query for giftcodes using new model (t_giftcode)
        $query = Giftcode::with(['logs'])
            ->select([
                't_giftcode.*'
            ]);

        // Apply search filters
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('t_giftcode.code', 'like', "%{$search}%")
                    ->orWhere('t_giftcode.content', 'like', "%{$search}%");
            });
        }

        // Apply status filter
        if ($statusFilter !== 'all') {
            switch ($statusFilter) {
                case 'active':
                    $query->where(function ($q) {
                        $q->where('t_giftcode.period', 0)
                            ->orWhereRaw('DATE_ADD(t_giftcode.created_at, INTERVAL t_giftcode.period DAY) > NOW()');
                    });
                    break;
                case 'expired':
                    $query->where('t_giftcode.period', '>', 0)
                        ->whereRaw('DATE_ADD(t_giftcode.created_at, INTERVAL t_giftcode.period DAY) <= NOW()');
                    break;
                case 'used_up':
                    $query->where('t_giftcode.limit', '>', 0)
                        ->whereRaw('(SELECT COUNT(*) FROM t_giftcode_log WHERE groupid = t_giftcode.id) >= t_giftcode.limit');
                    break;
            }
        }

        $giftcodes = $query->orderBy('t_giftcode.created_at', 'desc')->paginate(20);

        // Add usage count for each giftcode
        foreach ($giftcodes as $giftcode) {
            $giftcode->usage_count = $giftcode->getUsageCount();
            $giftcode->remaining_uses = $giftcode->limit > 0 ? max(0, $giftcode->limit - $giftcode->usage_count) : 'Không giới hạn';
            $giftcode->codes_display = implode(', ', $giftcode->getCodesArray());
        }

        // Get statistics
        $stats = [
            'total_giftcodes' => Giftcode::count(),
            'active_giftcodes' => Giftcode::where(function ($q) {
                    $q->where('period', 0)
                        ->orWhereRaw('DATE_ADD(created_at, INTERVAL period DAY) > NOW()');
                })
                ->count(),
            'expired_giftcodes' => Giftcode::where('period', '>', 0)
                ->whereRaw('DATE_ADD(created_at, INTERVAL period DAY) <= NOW()')
                ->count(),
            'total_usage' => GiftcodeLog::count()
        ];

        // Rename stats keys to match view
        $stats = [
            'total_codes' => $stats['total_giftcodes'],
            'active_codes' => $stats['active_giftcodes'],
            'expired_codes' => $stats['expired_giftcodes'],
            'total_usage' => $stats['total_usage']
        ];

        return view('admin.giftcode.index', compact('giftcodes', 'search', 'statusFilter', 'typeFilter', 'stats'));
    }

    public function create()
    {
        $admin = Session::get('admin_user');
        $itemList = ItemService::getItemsByCategory();
        $popularItems = ItemService::getPopularItems();
        return view('admin.giftcode.create', compact('admin', 'itemList', 'popularItems'));
    }

    /**
     * API endpoint to get item list
     */
    public function getItems(Request $request)
    {
        $search = $request->get('search');
        $category = $request->get('category');

        if ($search) {
            $items = ItemService::searchItems($search);
        } elseif ($category) {
            $itemsByCategory = ItemService::getItemsByCategory();
            $items = $itemsByCategory[$category] ?? [];
        } else {
            $items = ItemService::getItemList();
        }

        return response()->json([
            'success' => true,
            'items' => $items,
            'categories' => array_keys(ItemService::getItemsByCategory())
        ]);
    }

    public function store(Request $request)
    {
        $admin = Session::get('admin_user');

        $request->validate([
            'type' => 'required|integer|in:0,1,2',
            'content' => 'required|string|max:255',
            'items' => 'required|string',
            'limit' => 'required|integer|min:0',
            'period' => 'required|integer|min:0',
            'zoneid' => 'required|integer',
            'accounts' => 'nullable|string',
            'multiple' => 'required|boolean',
            'number' => 'required_if:multiple,true|integer|min:1|max:1000',
            'code' => 'required_if:multiple,false|string|max:50'
        ], [
            'type.required' => 'Loại giftcode là bắt buộc.',
            'content.required' => 'Nội dung giftcode là bắt buộc.',
            'items.required' => 'Danh sách vật phẩm là bắt buộc.',
            'limit.required' => 'Giới hạn sử dụng là bắt buộc.',
            'period.required' => 'Thời hạn sử dụng là bắt buộc.',
            'zoneid.required' => 'Server áp dụng là bắt buộc.',
            'number.required_if' => 'Số lượng code là bắt buộc khi tạo nhiều code.',
            'code.required_if' => 'Mã giftcode là bắt buộc khi tạo code đơn.',
        ]);

        // Validate items format
        $items = explode(PHP_EOL, trim($request->items));
        foreach ($items as $item) {
            $parts = explode(',', trim($item));
            if (count($parts) !== 7) {
                return redirect()->back()->withErrors([
                    'items' => 'Định dạng item không đúng. Cần 7 tham số: goodsid,count,binding,forge_level,appendproplev,lucky,excellenceinfo'
                ])->withInput();
            }
        }

        // Prepare data for service
        $data = [
            'type' => $request->type,
            'accounts' => $request->accounts,
            'multiple' => $request->multiple,
            'code' => $request->code,
            'number' => $request->number,
            'items' => $items,
            'content' => $request->content,
            'limit' => $request->limit,
            'period' => $request->period,
            'zoneid' => $request->zoneid
        ];

        // Create giftcode using service
        $result = $this->giftcodeService->createGiftcode($data);

        if (!$result['success']) {
            return redirect()->back()->withErrors([
                'error' => $result['message']
            ])->withInput();
        }

        $giftcode = $result['giftcode'];
        $codes = $result['codes'];

        // Log admin action
        $this->logAdminAction(
            $admin,
            'create_giftcode',
            'giftcode',
            $giftcode->id,
            $request->content,
            [], // No old data
            [
                'type' => $request->type,
                'code_count' => count($codes),
                'codes' => $codes,
                'items' => $items,
                'limit' => $request->limit,
                'period' => $request->period,
                'zoneid' => $request->zoneid,
            ],
            "Tạo " . count($codes) . " giftcode: " . $request->content,
            $request->ip()
        );

        return redirect()->route('admin.giftcode.index')
            ->with('success', "Đã tạo thành công " . count($codes) . " giftcode!");
    }

    public function show($id)
    {
        $admin = Session::get('admin_user');

        $giftcode = Giftcode::find($id);

        if (!$giftcode) {
            return redirect()->route('admin.giftcode.index')->withErrors(['error' => 'Không tìm thấy giftcode.']);
        }

        // Get usage history from giftcode logs
        $usageHistory = GiftcodeLog::where('groupid', $id)
            ->orderBy('created_at', 'desc')
            ->limit(50)
            ->get();

        // Get codes array for display
        $codes = $giftcode->getCodesArray();

        return view('admin.giftcode.show', compact('admin', 'giftcode', 'usageHistory', 'codes'));
    }

    public function edit($id)
    {
        $admin = Session::get('admin_user');

        $giftcode = Giftcode::find($id);

        if (!$giftcode) {
            return redirect()->route('admin.giftcode.index')->withErrors(['error' => 'Không tìm thấy giftcode.']);
        }

        $itemList = ItemService::getItemsByCategory();
        $popularItems = ItemService::getPopularItems();

        return view('admin.giftcode.edit', compact('admin', 'giftcode', 'itemList', 'popularItems'));
    }

    public function update(Request $request, $id)
    {
        $admin = Session::get('admin_user');

        $request->validate([
            'type' => 'required|integer|in:0,1,2',
            'content' => 'required|string|max:255',
            'name' => 'nullable|string|max:255',
            'items' => 'required|string',
            'limit' => 'required|integer|min:0',
            'period' => 'required|integer|min:0',
            'accounts' => 'nullable|string',
            'characters' => 'nullable|string',
            'is_active' => 'boolean',
        ], [
            'type.required' => 'Loại giftcode là bắt buộc.',
            'content.required' => 'Nội dung giftcode là bắt buộc.',
            'items.required' => 'Danh sách vật phẩm là bắt buộc.',
            'limit.required' => 'Giới hạn sử dụng là bắt buộc.',
            'period.required' => 'Thời hạn sử dụng là bắt buộc.',
        ]);

        // Get giftcode info before update
        $giftcode = Giftcode::find($id);
        if (!$giftcode) {
            return redirect()->route('admin.giftcode.index')->withErrors(['error' => 'Không tìm thấy giftcode.']);
        }

        // Validate items format
        $items = explode(PHP_EOL, trim($request->items));
        foreach ($items as $item) {
            $parts = explode(',', trim($item));
            if (count($parts) !== 7) {
                return redirect()->back()->withErrors([
                    'items' => 'Định dạng item không đúng. Cần 7 tham số: goodsid,count,binding,forge_level,appendproplev,lucky,excellenceinfo'
                ])->withInput();
            }
        }

        // Validate character-specific requirements
        if ($request->type == 0 && !empty($request->characters)) {
            // Validate character names exist in database
            $characterNames = array_map('trim', explode(',', $request->characters));
            $validCharacters = DB::connection('game_mysql')
                ->table('t_character')
                ->whereIn('name', $characterNames)
                ->pluck('name')
                ->toArray();

            $invalidCharacters = array_diff($characterNames, $validCharacters);
            if (!empty($invalidCharacters)) {
                return redirect()->back()->withErrors([
                    'characters' => 'Các nhân vật sau không tồn tại: ' . implode(', ', $invalidCharacters)
                ])->withInput();
            }
        }

        $oldData = [
            'type' => $giftcode->type,
            'content' => $giftcode->content,
            'name' => $giftcode->name,
            'limit' => $giftcode->limit,
            'period' => $giftcode->period,
            'accounts' => $giftcode->accounts,
            'characters' => $giftcode->characters ?? '',
            'is_active' => $giftcode->is_active,
        ];

        $newData = [
            'type' => $request->type,
            'content' => $request->content,
            'name' => $request->name,
            'limit' => $request->limit,
            'period' => $request->period,
            'accounts' => $request->accounts,
            'characters' => $request->characters,
            'is_active' => $request->has('is_active'),
        ];

        // Prepare update data
        $updateData = [
            'type' => $request->type,
            'content' => $request->content,
            'name' => $request->name,
            'items' => $items,
            'limit' => $request->limit,
            'period' => $request->period,
            'is_active' => $request->has('is_active'),
        ];

        // Handle type-specific fields
        if ($request->type == 2) {
            // Private giftcode - store accounts
            $updateData['accounts'] = $request->accounts;
            $updateData['characters'] = null;
        } elseif ($request->type == 0) {
            // Character-specific giftcode - store characters
            $updateData['characters'] = $request->characters;
            $updateData['accounts'] = null;
        } else {
            // Public giftcode - clear both
            $updateData['accounts'] = null;
            $updateData['characters'] = null;
        }

        // Update giftcode
        $giftcode->update($updateData);

        // Log admin action
        $this->logAdminAction(
            $admin,
            'edit_giftcode',
            'giftcode',
            $id,
            $giftcode->content,
            $oldData,
            $newData,
            'Cập nhật thông tin giftcode',
            $request->ip()
        );

        return redirect()->route('admin.giftcode.show', $id)
            ->with('success', "Đã cập nhật giftcode thành công.");
    }

    public function destroy($id)
    {
        $admin = Session::get('admin_user');

        // Get giftcode info before delete
        $giftcode = Giftcode::find($id);
        if (!$giftcode) {
            return response()->json(['success' => false, 'message' => 'Không tìm thấy giftcode']);
        }

        $codes = $giftcode->getCodesArray();

        // Delete giftcode
        $giftcode->delete();

        // Log admin action
        $this->logAdminAction(
            $admin,
            'delete_giftcode',
            'giftcode',
            $id,
            $giftcode->content,
            ['codes' => $codes, 'content' => $giftcode->content],
            [],
            'Xóa giftcode',
            request()->ip()
        );

        return response()->json(['success' => true, 'message' => 'Đã xóa giftcode thành công']);
    }

    public function toggleStatus($id)
    {
        $admin = Session::get('admin_user');

        $giftcode = Giftcode::find($id);
        if (!$giftcode) {
            return response()->json(['success' => false, 'message' => 'Không tìm thấy giftcode']);
        }

        $newStatus = !$giftcode->is_active;

        $giftcode->update(['is_active' => $newStatus]);

        // Log admin action
        $this->logAdminAction(
            $admin,
            'toggle_giftcode_status',
            'giftcode',
            $id,
            $giftcode->content,
            ['is_active' => !$newStatus],
            ['is_active' => $newStatus],
            $newStatus ? 'Kích hoạt giftcode' : 'Vô hiệu hóa giftcode',
            request()->ip()
        );

        return response()->json([
            'success' => true,
            'message' => $newStatus ? 'Đã kích hoạt giftcode' : 'Đã vô hiệu hóa giftcode',
            'is_active' => $newStatus
        ]);
    }

    public function usageReport(Request $request)
    {
        $admin = Session::get('admin_user');

        // Get date range from request or default to last 30 days
        $startDate = $request->get('start_date', now()->subDays(30)->format('Y-m-d'));
        $endDate = $request->get('end_date', now()->format('Y-m-d'));

        // Get giftcode usage statistics
        $usageStats = DB::table('giftcode_logs as gl')
            ->join('giftcodes as g', 'gl.groupid', '=', 'g.id')
            ->select([
                'g.id',
                'g.code',
                'g.name',
                'g.limit',
                DB::raw('COUNT(gl.id) as total_usage'),
                DB::raw('DATE(gl.created_at) as usage_date')
            ])
            ->whereBetween('gl.created_at', [$startDate, $endDate])
            ->groupBy('g.id', 'g.code', 'g.name', 'g.limit', 'usage_date')
            ->orderBy('usage_date', 'desc')
            ->orderBy('total_usage', 'desc')
            ->get();

        // Get top used giftcodes
        $topGiftcodes = DB::table('giftcode_logs as gl')
            ->join('giftcodes as g', 'gl.groupid', '=', 'g.id')
            ->select([
                'g.id',
                'g.code',
                'g.name',
                'g.limit',
                DB::raw('COUNT(gl.id) as total_usage')
            ])
            ->whereBetween('gl.created_at', [$startDate, $endDate])
            ->groupBy('g.id', 'g.code', 'g.name', 'g.limit')
            ->orderBy('total_usage', 'desc')
            ->limit(10)
            ->get();

        // Get daily usage summary
        $dailyUsage = DB::table('giftcode_logs')
            ->select([
                DB::raw('DATE(created_at) as date'),
                DB::raw('COUNT(*) as total_usage')
            ])
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy('date')
            ->orderBy('date', 'desc')
            ->get();

        return view('admin.giftcode.usage-report', compact(
            'admin',
            'usageStats',
            'topGiftcodes',
            'dailyUsage',
            'startDate',
            'endDate'
        ));
    }

    private function logAdminAction($admin, $action, $targetType, $targetId, $targetName, $oldData, $newData, $reason, $ip)
    {
        DB::table('admin_action_logs')->insert([
            'admin_id' => $admin['id'],
            'admin_username' => $admin['username'],
            'action' => $action,
            'target_type' => $targetType,
            'target_id' => $targetId,
            'target_name' => $targetName,
            'old_data' => json_encode($oldData),
            'new_data' => json_encode($newData),
            'reason' => $reason,
            'ip_address' => $ip,
            'user_agent' => request()->header('User-Agent'),
            'created_at' => now(),
            'updated_at' => now()
        ]);
    }
}
