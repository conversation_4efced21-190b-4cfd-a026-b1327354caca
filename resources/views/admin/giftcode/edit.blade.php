@extends('layouts.admin')

@section('title', 'Chỉnh sửa Giftcode')

@section('content')
<div class="giftcode-edit-container">
    <!-- Header Section -->
    <div class="page-header-modern">
        <div class="header-content">
            <div class="header-icon">
                <i class="fas fa-edit"></i>
            </div>
            <div class="header-text">
                <h1 class="page-title">Chỉnh sửa Giftcode</h1>
                <p class="page-subtitle">Cập nhật thông tin mã quà tặng cho người chơi MU Online</p>
            </div>
        </div>
        <div class="header-actions">
            <a href="{{ route('admin.giftcode.show', $giftcode->id) }}" class="btn btn-glass">
                <i class="fas fa-eye"></i>
                <span>Xem chi tiết</span>
            </a>
            <a href="{{ route('admin.giftcode.index') }}" class="btn btn-glass">
                <i class="fas fa-arrow-left"></i>
                <span>Quay lại</span>
            </a>
        </div>
    </div>

    <form method="POST" action="{{ route('admin.giftcode.update', $giftcode->id) }}" class="giftcode-form">
        @csrf
        @method('PUT')
        <!-- Basic Information Card -->
        <div class="form-card">
            <div class="card-header-modern">
                <div class="card-icon">
                    <i class="fas fa-info-circle"></i>
                </div>
                <h3 class="card-title">Thông tin cơ bản</h3>
            </div>
            <div class="card-body-modern">
                <div class="form-grid">
                    <div class="form-group-modern">
                        <label for="name" class="form-label-modern">
                            <i class="fas fa-signature"></i>
                            Tên Giftcode
                        </label>
                        <input type="text"
                               class="form-input-modern @error('name') is-invalid @enderror"
                               id="name"
                               name="name"
                               value="{{ old('name', $giftcode->name) }}"
                               placeholder="Ví dụ: Giftcode chào mừng năm mới">
                        @error('name')
                            <div class="error-message">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group-modern">
                        <label for="type" class="form-label-modern">
                            <i class="fas fa-tag"></i>
                            Loại Giftcode
                            <span class="required">*</span>
                        </label>
                        <select class="form-input-modern @error('type') is-invalid @enderror"
                                id="type"
                                name="type">
                            <option value="1" {{ old('type', $giftcode->type) == 1 ? 'selected' : '' }}>Công khai - Ai cũng dùng được</option>
                            <option value="2" {{ old('type', $giftcode->type) == 2 ? 'selected' : '' }}>Riêng tư - Chỉ tài khoản cụ thể</option>
                            <option value="0" {{ old('type', $giftcode->type) == 0 ? 'selected' : '' }}>Theo nhân vật - Mỗi nhân vật dùng 1 lần</option>
                        </select>
                        @error('type')
                            <div class="error-message">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="form-grid">
                    <div class="form-group-modern">
                        <label for="limit" class="form-label-modern">
                            <i class="fas fa-users"></i>
                            Giới hạn sử dụng
                            <span class="required">*</span>
                        </label>
                        <input type="number"
                               class="form-input-modern @error('limit') is-invalid @enderror"
                               id="limit"
                               name="limit"
                               value="{{ old('limit', $giftcode->limit) }}"
                               min="0"
                               placeholder="0 = Không giới hạn">
                        @error('limit')
                            <div class="error-message">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group-modern">
                        <label for="period" class="form-label-modern">
                            <i class="fas fa-clock"></i>
                            Thời hạn (ngày)
                            <span class="required">*</span>
                        </label>
                        <input type="number"
                               class="form-input-modern @error('period') is-invalid @enderror"
                               id="period"
                               name="period"
                               value="{{ old('period', $giftcode->period) }}"
                               min="0"
                               placeholder="0 = Không hết hạn">
                        @error('period')
                            <div class="error-message">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="form-group-modern full-width">
                    <label for="content" class="form-label-modern">
                        <i class="fas fa-align-left"></i>
                        Nội dung giftcode
                        <span class="required">*</span>
                    </label>
                    <textarea class="form-textarea-modern @error('content') is-invalid @enderror"
                              id="content"
                              name="content"
                              rows="3"
                              placeholder="Nội dung mô tả giftcode này...">{{ old('content', $giftcode->content) }}</textarea>
                    @error('content')
                        <div class="error-message">{{ $message }}</div>
                    @enderror
                </div>

                <!-- Accounts field for private type -->
                <div class="form-group-modern full-width" id="accounts_section" style="{{ old('type', $giftcode->type) == 2 ? '' : 'display: none;' }}">
                    <label for="accounts" class="form-label-modern">
                        <i class="fas fa-users"></i>
                        Danh sách tài khoản được phép
                        <span class="required">*</span>
                    </label>
                    <textarea class="form-textarea-modern @error('accounts') is-invalid @enderror"
                              id="accounts"
                              name="accounts"
                              rows="3"
                              placeholder="Nhập tên tài khoản, cách nhau bằng dấu phẩy. Ví dụ: user1,user2,user3">{{ old('accounts', $giftcode->accounts) }}</textarea>
                    @error('accounts')
                        <div class="error-message">{{ $message }}</div>
                    @enderror
                </div>

                <!-- Characters field for character-specific type -->
                <div class="form-group-modern full-width" id="characters_section" style="{{ old('type', $giftcode->type) == 0 ? '' : 'display: none;' }}">
                    <label for="characters" class="form-label-modern">
                        <i class="fas fa-user-ninja"></i>
                        Danh sách nhân vật được phép
                        <span class="required">*</span>
                    </label>
                    <textarea class="form-textarea-modern @error('characters') is-invalid @enderror"
                              id="characters"
                              name="characters"
                              rows="3"
                              placeholder="Nhập tên nhân vật, cách nhau bằng dấu phẩy. Ví dụ: Character1,Character2,Character3">{{ old('characters', $giftcode->characters ?? '') }}</textarea>
                    <small class="form-hint">Để trống nếu muốn tất cả nhân vật đều có thể sử dụng (mỗi nhân vật chỉ dùng 1 lần)</small>
                    @error('characters')
                        <div class="error-message">{{ $message }}</div>
                    @enderror
                </div>
            </div>
        </div>

        <!-- Items Reward Card with Shopping Cart Interface -->
        <div class="form-card">
            <div class="card-header-modern">
                <div class="card-icon">
                    <i class="fas fa-gift"></i>
                </div>
                <h3 class="card-title">Vật phẩm thưởng</h3>
            </div>
            <div class="card-body-modern">
                <div class="item-selection-container">
                    <!-- Shopping Cart Interface -->
                    <div class="shopping-cart-interface">
                        <!-- Header Notice -->
                        <div class="interface-header">
                            <h4><i class="fas fa-shopping-cart"></i> Giao diện chỉnh sửa vật phẩm</h4>
                            <div class="legacy-notice">
                                <div class="notice-icon">ℹ️</div>
                                <div class="notice-content">
                                    <strong>Lưu ý:</strong> Sử dụng Legacy ID (14,15,16...) để tương thích với game hiện tại.
                                    Hệ thống sẽ tự động chuyển đổi khi cần thiết.
                                </div>
                            </div>
                        </div>

                        <!-- Popular Items -->
                        <div class="quick-add-section">
                            <h5><i class="fas fa-star"></i> Vật phẩm phổ biến - Click để thêm</h5>
                            <div class="popular-items-grid">
                                <!-- Jewels (Legacy IDs) -->
                                <div class="popular-item" onclick="quickAddItem(14, 'Jewel of Bless', '💎')">
                                    <div class="item-icon">💎</div>
                                    <div class="item-name">Jewel of Bless</div>
                                    <div class="item-id">ID: 14</div>
                                </div>
                                <div class="popular-item" onclick="quickAddItem(15, 'Jewel of Soul', '💠')">
                                    <div class="item-icon">💠</div>
                                    <div class="item-name">Jewel of Soul</div>
                                    <div class="item-id">ID: 15</div>
                                </div>
                                <div class="popular-item" onclick="quickAddItem(16, 'Jewel of Life', '🔮')">
                                    <div class="item-icon">🔮</div>
                                    <div class="item-name">Jewel of Life</div>
                                    <div class="item-id">ID: 16</div>
                                </div>
                                <div class="popular-item" onclick="quickAddItem(13, 'Jewel of Chaos', '🌀')">
                                    <div class="item-icon">🌀</div>
                                    <div class="item-name">Jewel of Chaos</div>
                                    <div class="item-id">ID: 13</div>
                                </div>
                                <div class="popular-item" onclick="quickAddItem(22, 'Jewel of Creation', '✨')">
                                    <div class="item-icon">✨</div>
                                    <div class="item-name">Jewel of Creation</div>
                                    <div class="item-id">ID: 22</div>
                                </div>
                                <div class="popular-item" onclick="quickAddItem(1011, 'Premium Equipment', '⚔️')">
                                    <div class="item-icon">⚔️</div>
                                    <div class="item-name">Premium Equipment</div>
                                    <div class="item-id">ID: 1011</div>
                                </div>
                            </div>
                        </div>

                        <!-- Manual Add Item -->
                        <div class="manual-add-section">
                            <h5><i class="fas fa-plus"></i> Thêm vật phẩm thủ công</h5>
                            <div class="add-item-form">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label>Item ID:</label>
                                        <input type="number" id="manual_item_id" placeholder="Ví dụ: 50014" class="form-input">
                                    </div>
                                    <div class="form-group">
                                        <label>Tên vật phẩm:</label>
                                        <input type="text" id="manual_item_name" placeholder="Ví dụ: Jewel of Bless" class="form-input">
                                    </div>
                                    <div class="form-group">
                                        <label>Số lượng:</label>
                                        <input type="number" id="manual_item_quantity" value="1" min="1" max="999" class="form-input">
                                    </div>
                                    <div class="form-group">
                                        <button type="button" onclick="addManualItem()" class="add-btn">
                                            <i class="fas fa-plus"></i> Thêm
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Shopping Cart -->
                        <div class="cart-section">
                            <h5><i class="fas fa-shopping-cart"></i> Giỏ hàng vật phẩm</h5>
                            <div id="cart_items" class="cart-items">
                                <!-- Items will be populated by JavaScript -->
                            </div>
                            <div class="cart-summary">
                                <div class="total-items">Tổng: <span id="total_items">0</span> loại vật phẩm</div>
                                <button type="button" onclick="clearCart()" class="clear-cart-btn">
                                    <i class="fas fa-trash"></i> Xóa tất cả
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Hidden textarea for form submission -->
                    <textarea id="items" name="items" class="item-textarea" style="display:none;">{{ old('items', implode(PHP_EOL, $giftcode->getItemsArray())) }}</textarea>
                    @error('items')
                        <div class="error-message">{{ $message }}</div>
                    @enderror
                </div>
            </div>
        </div>

        <!-- Code Display Card -->
        <div class="form-card">
            <div class="card-header-modern">
                <div class="card-icon">
                    <i class="fas fa-code"></i>
                </div>
                <h3 class="card-title">Mã Code (Không thể thay đổi)</h3>
            </div>
            <div class="card-body-modern">
                @php
                    $codes = $giftcode->getCodesArray();
                @endphp

                @if(count($codes) > 1)
                    <div class="code-display-multiple">
                        <div class="code-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>Giftcode này có {{ count($codes) }} mã code:</strong>
                        </div>
                        <div class="codes-preview">
                            @foreach(array_slice($codes, 0, 5) as $code)
                                <span class="code-badge">{{ $code }}</span>
                            @endforeach
                            @if(count($codes) > 5)
                                <span class="code-more">... và {{ count($codes) - 5 }} mã khác</span>
                            @endif
                        </div>
                    </div>
                @else
                    <div class="code-display-single">
                        <div class="code-info">
                            <i class="fas fa-key"></i>
                            <strong>Mã code:</strong>
                        </div>
                        <div class="single-code">
                            <span class="code-badge">{{ $codes[0] ?? 'N/A' }}</span>
                        </div>
                    </div>
                @endif
            </div>
        </div>


        <!-- Settings Card -->
        <div class="form-card">
            <div class="card-header-modern">
                <div class="card-icon">
                    <i class="fas fa-cogs"></i>
                </div>
                <h3 class="card-title">Cài đặt</h3>
            </div>
            <div class="card-body-modern">
                <div class="form-group-modern">
                    <label class="form-label-modern">
                        <i class="fas fa-toggle-on"></i>
                        Trạng thái
                    </label>
                    <div class="checkbox-modern">
                        <input type="checkbox"
                               name="is_active"
                               id="is_active"
                               value="1"
                               {{ old('is_active', $giftcode->is_active) ? 'checked' : '' }}>
                        <label for="is_active" class="checkbox-label">
                            <span class="checkbox-custom"></span>
                            <span class="checkbox-text">Kích hoạt giftcode</span>
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <!-- Current Stats Card -->
        <div class="form-card">
            <div class="card-header-modern">
                <div class="card-icon">
                    <i class="fas fa-chart-bar"></i>
                </div>
                <h3 class="card-title">Thống kê hiện tại</h3>
            </div>
            <div class="card-body-modern">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-value">{{ number_format($giftcode->getUsageCount()) }}</div>
                            <div class="stat-label">Đã sử dụng</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-content">
                            @php $remaining = $giftcode->limit > 0 ? max(0, $giftcode->limit - $giftcode->getUsageCount()) : 'Không giới hạn'; @endphp
                            <div class="stat-value">{{ is_numeric($remaining) ? number_format($remaining) : $remaining }}</div>
                            <div class="stat-label">Còn lại</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-percentage"></i>
                        </div>
                        <div class="stat-content">
                            @php $usagePercent = $giftcode->limit > 0 ? ($giftcode->getUsageCount() / $giftcode->limit) * 100 : 0; @endphp
                            <div class="stat-value">{{ number_format($usagePercent, 1) }}%</div>
                            <div class="stat-label">Tỷ lệ sử dụng</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-power-off"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-value">
                                @if($giftcode->is_active)
                                    <span class="status-active">Hoạt động</span>
                                @else
                                    <span class="status-inactive">Vô hiệu</span>
                                @endif
                            </div>
                            <div class="stat-label">Trạng thái</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Submit Actions -->
        <div class="form-actions">
            <button type="submit" class="btn btn-primary-modern">
                <i class="fas fa-save"></i>
                <span>Cập nhật Giftcode</span>
            </button>
            <a href="{{ route('admin.giftcode.show', $giftcode->id) }}" class="btn btn-secondary-modern">
                <i class="fas fa-times"></i>
                <span>Hủy</span>
            </a>
        </div>
    </form>
</div>

<style>
/* Modern Giftcode Edit Styles */
.giftcode-edit-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 20px;
}

/* Header Styles */
.page-header-modern {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 25px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.header-content {
    display: flex;
    align-items: center;
    gap: 20px;
}

.header-icon {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
}

.header-text h1 {
    color: white;
    margin: 0;
    font-size: 28px;
    font-weight: 700;
}

.page-subtitle {
    color: rgba(255, 255, 255, 0.8);
    margin: 5px 0 0 0;
    font-size: 16px;
}

.header-actions {
    display: flex;
    gap: 15px;
}

.btn-glass {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 12px 20px;
    border-radius: 12px;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.btn-glass:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
}

/* Form Styles */
.giftcode-form {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.form-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.card-header-modern {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 25px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.card-icon {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
}

.card-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.card-body-modern {
    padding: 30px;
}

/* Form Grid */
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
    margin-bottom: 25px;
}

.form-group-modern {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group-modern.full-width {
    grid-column: 1 / -1;
}

.form-label-modern {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: #2d3748;
    font-size: 14px;
}

.form-label-modern i {
    color: #667eea;
}

.required {
    color: #e53e3e;
}

.form-input-modern,
.form-textarea-modern {
    padding: 12px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: white;
}

.form-input-modern:focus,
.form-textarea-modern:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-hint {
    font-size: 12px;
    color: #718096;
    margin-top: 4px;
}

.error-message {
    color: #e53e3e;
    font-size: 12px;
    margin-top: 4px;
}

/* Checkbox Styles */
.checkbox-modern {
    display: flex;
    align-items: center;
    gap: 12px;
}

.checkbox-modern input[type="checkbox"] {
    display: none;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    font-weight: 500;
    color: #2d3748;
}

.checkbox-custom {
    width: 20px;
    height: 20px;
    border: 2px solid #e2e8f0;
    border-radius: 6px;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-modern input[type="checkbox"]:checked + .checkbox-label .checkbox-custom {
    background: #667eea;
    border-color: #667eea;
}

.checkbox-modern input[type="checkbox"]:checked + .checkbox-label .checkbox-custom::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

/* Shopping Cart Interface Styles */
.shopping-cart-interface {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 25px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.interface-header {
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.interface-header h4 {
    color: #2d3748;
    margin: 0 0 15px 0;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.legacy-notice {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 15px;
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 10px;
    backdrop-filter: blur(10px);
}

.notice-icon {
    font-size: 18px;
    flex-shrink: 0;
    margin-top: 2px;
}

.notice-content {
    color: #2d3748;
    font-size: 14px;
    line-height: 1.5;
}

/* Popular Items */
.quick-add-section {
    margin-bottom: 25px;
}

.quick-add-section h5 {
    color: #2d3748;
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.popular-items-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
}

.popular-item {
    background: white;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    padding: 15px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.popular-item:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
}

.item-icon {
    font-size: 24px;
    margin-bottom: 8px;
}

.item-name {
    font-weight: 600;
    color: #2d3748;
    font-size: 12px;
    margin-bottom: 4px;
}

.item-id {
    font-size: 11px;
    color: #718096;
}

/* Manual Add Section */
.manual-add-section {
    margin-bottom: 25px;
}

.manual-add-section h5 {
    color: #2d3748;
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.add-item-form .form-row {
    display: grid;
    grid-template-columns: 1fr 2fr 1fr auto;
    gap: 15px;
    align-items: end;
}

.add-item-form .form-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.add-item-form .form-group label {
    font-size: 12px;
    font-weight: 600;
    color: #2d3748;
}

.add-item-form .form-input {
    padding: 8px 12px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 14px;
}

.add-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: all 0.3s ease;
}

.add-btn:hover {
    background: #5a67d8;
    transform: translateY(-1px);
}

/* Cart Section */
.cart-section {
    background: white;
    border-radius: 12px;
    padding: 20px;
    border: 2px solid #e2e8f0;
}

.cart-section h5 {
    color: #2d3748;
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.cart-items {
    min-height: 100px;
    margin-bottom: 15px;
}

.empty-cart {
    text-align: center;
    color: #718096;
    padding: 30px;
}

.empty-cart i {
    font-size: 48px;
    margin-bottom: 10px;
    opacity: 0.5;
}

.cart-summary {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 15px;
    border-top: 1px solid #e2e8f0;
}

.total-items {
    font-weight: 600;
    color: #2d3748;
}

.clear-cart-btn {
    background: #e53e3e;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: all 0.3s ease;
}

.clear-cart-btn:hover {
    background: #c53030;
}

/* Cart Item Styles */
.cart-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: #f7fafc;
    border-radius: 10px;
    margin-bottom: 10px;
    border: 1px solid #e2e8f0;
}

.item-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.item-icon {
    font-size: 20px;
}

.item-details {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.item-name {
    font-weight: 600;
    color: #2d3748;
    font-size: 14px;
}

.item-id {
    font-size: 12px;
    color: #718096;
}

.item-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.qty-btn {
    width: 30px;
    height: 30px;
    border: 1px solid #e2e8f0;
    background: white;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: #2d3748;
    transition: all 0.2s ease;
}

.qty-btn:hover {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.quantity {
    min-width: 30px;
    text-align: center;
    font-weight: 600;
    color: #2d3748;
}

.remove-btn {
    width: 30px;
    height: 30px;
    border: 1px solid #e53e3e;
    background: #e53e3e;
    color: white;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.remove-btn:hover {
    background: #c53030;
    border-color: #c53030;
}

/* Code Display Styles */
.code-display-multiple,
.code-display-single {
    background: rgba(102, 126, 234, 0.05);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid rgba(102, 126, 234, 0.2);
}

.code-info {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
    color: #2d3748;
    font-weight: 600;
}

.codes-preview {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    align-items: center;
}

.code-badge {
    background: #667eea;
    color: white;
    padding: 6px 12px;
    border-radius: 8px;
    font-family: 'Courier New', monospace;
    font-weight: 600;
    font-size: 14px;
}

.code-more {
    color: #718096;
    font-style: italic;
}

.single-code {
    display: flex;
    justify-content: center;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.stat-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    border: 2px solid #e2e8f0;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: all 0.3s ease;
}

.stat-card:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.1);
}

.stat-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.stat-content {
    flex: 1;
}

.stat-value {
    font-size: 24px;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 14px;
    color: #718096;
    font-weight: 500;
}

.status-active {
    color: #38a169;
    font-weight: 600;
}

.status-inactive {
    color: #e53e3e;
    font-weight: 600;
}

/* Form Actions */
.form-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    padding: 20px;
}

.btn-primary-modern,
.btn-secondary-modern {
    padding: 15px 30px;
    border-radius: 12px;
    font-weight: 600;
    font-size: 16px;
    display: flex;
    align-items: center;
    gap: 10px;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 160px;
    justify-content: center;
}

.btn-primary-modern {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
    color: white;
    text-decoration: none;
}

.btn-secondary-modern {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.btn-secondary-modern:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-header-modern {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }

    .header-actions {
        justify-content: center;
    }

    .form-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .add-item-form .form-row {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .form-actions {
        flex-direction: column;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const typeSelect = document.getElementById('type');
    const accountsSection = document.getElementById('accounts_section');
    const charactersSection = document.getElementById('characters_section');

    let cartItems = [];

    // Toggle sections based on giftcode type
    function toggleSections() {
        const selectedType = typeSelect.value;

        // Hide all sections first
        accountsSection.style.display = 'none';
        charactersSection.style.display = 'none';

        // Show relevant section
        if (selectedType == '2') {
            accountsSection.style.display = '';
        } else if (selectedType == '0') {
            charactersSection.style.display = '';
        }
    }

    typeSelect.addEventListener('change', toggleSections);
    toggleSections(); // Initial call

    // Initialize cart with existing items
    function initializeCart() {
        const itemsTextarea = document.getElementById('items');
        const itemsText = itemsTextarea.value.trim();

        if (itemsText) {
            const lines = itemsText.split('\n');
            lines.forEach(line => {
                const parts = line.split(',');
                if (parts.length >= 2) {
                    const itemId = parts[0].trim();
                    const quantity = parseInt(parts[1]) || 1;

                    cartItems.push({
                        id: itemId,
                        name: `Item ${itemId}`,
                        icon: '📦',
                        quantity: quantity
                    });
                }
            });
        }

        updateCartDisplay();
    }

    // Quick add item from popular items
    window.quickAddItem = function(itemId, itemName, itemIcon) {
        const existingIndex = cartItems.findIndex(item => item.id == itemId);

        if (existingIndex !== -1) {
            // Item already exists, increase quantity
            cartItems[existingIndex].quantity++;
        } else {
            // Add new item
            cartItems.push({
                id: itemId,
                name: itemName,
                icon: itemIcon,
                quantity: 1
            });
        }

        updateCartDisplay();
        updateHiddenTextarea();
    };

    // Add item manually
    window.addManualItem = function() {
        const itemId = document.getElementById('manual_item_id').value;
        const itemName = document.getElementById('manual_item_name').value;
        const quantity = parseInt(document.getElementById('manual_item_quantity').value) || 1;

        if (!itemId || !itemName) {
            alert('Vui lòng nhập đầy đủ Item ID và tên vật phẩm');
            return;
        }

        const existingIndex = cartItems.findIndex(item => item.id == itemId);

        if (existingIndex !== -1) {
            cartItems[existingIndex].quantity += quantity;
        } else {
            cartItems.push({
                id: itemId,
                name: itemName,
                icon: '📦',
                quantity: quantity
            });
        }

        // Clear form
        document.getElementById('manual_item_id').value = '';
        document.getElementById('manual_item_name').value = '';
        document.getElementById('manual_item_quantity').value = '1';

        updateCartDisplay();
        updateHiddenTextarea();
    };

    // Update cart display
    function updateCartDisplay() {
        const cartContainer = document.getElementById('cart_items');
        const totalItemsSpan = document.getElementById('total_items');

        if (cartItems.length === 0) {
            cartContainer.innerHTML = `
                <div class="empty-cart">
                    <i class="fas fa-shopping-cart"></i>
                    <p>Giỏ hàng trống. Hãy thêm vật phẩm ở trên.</p>
                </div>
            `;
        } else {
            cartContainer.innerHTML = cartItems.map((item, index) => `
                <div class="cart-item">
                    <div class="item-info">
                        <span class="item-icon">${item.icon}</span>
                        <div class="item-details">
                            <div class="item-name">${item.name}</div>
                            <div class="item-id">ID: ${item.id}</div>
                        </div>
                    </div>
                    <div class="item-controls">
                        <button type="button" onclick="decreaseQuantity(${index})" class="qty-btn">-</button>
                        <span class="quantity">${item.quantity}</span>
                        <button type="button" onclick="increaseQuantity(${index})" class="qty-btn">+</button>
                        <button type="button" onclick="removeItem(${index})" class="remove-btn">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `).join('');
        }

        totalItemsSpan.textContent = cartItems.length;
    }

    // Quantity controls
    window.increaseQuantity = function(index) {
        cartItems[index].quantity++;
        updateCartDisplay();
        updateHiddenTextarea();
    };

    window.decreaseQuantity = function(index) {
        if (cartItems[index].quantity > 1) {
            cartItems[index].quantity--;
        } else {
            cartItems.splice(index, 1);
        }
        updateCartDisplay();
        updateHiddenTextarea();
    };

    window.removeItem = function(index) {
        cartItems.splice(index, 1);
        updateCartDisplay();
        updateHiddenTextarea();
    };

    window.clearCart = function() {
        if (confirm('Bạn có chắc muốn xóa tất cả vật phẩm?')) {
            cartItems = [];
            updateCartDisplay();
            updateHiddenTextarea();
        }
    };

    // Update hidden textarea for form submission
    function updateHiddenTextarea() {
        const itemsTextarea = document.getElementById('items');
        const itemLines = cartItems.map(item =>
            `${item.id},${item.quantity},0,0,0,0,0`
        );
        itemsTextarea.value = itemLines.join('\n');
    }

    // Initialize cart on page load
    initializeCart();
});
</script>
@endsection
