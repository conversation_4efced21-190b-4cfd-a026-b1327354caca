<?php

use Illuminate\Support\Facades\Session;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

// Default route - redirect to User Site
Route::get('/', function () {
	return redirect('/user');
});

// Language switching routes
Route::get('/language/{locale}', [App\Http\Controllers\LanguageController::class, 'switchLanguage'])->name('language.switch');
Route::get('/api/language/current', [App\Http\Controllers\LanguageController::class, 'getCurrentLocale'])->name('language.current');

// Health check endpoint for Docker
Route::get('/health', function () {
    try {
        // Check database connection
        DB::connection()->getPdo();

        // Check Redis connection
        if (config('cache.default') === 'redis') {
            Cache::put('health_check', 'ok', 10);
            Cache::get('health_check');
        }

        return response('healthy', 200)
            ->header('Content-Type', 'text/plain');
    } catch (Exception $e) {
        return response('unhealthy: ' . $e->getMessage(), 503)
            ->header('Content-Type', 'text/plain');
    }
});





// User Site Routes
Route::group(['prefix' => 'user', 'middleware' => 'web'], function () {
	// Authentication routes (no middleware)
	Route::get('/login', [App\Http\Controllers\User\AuthController::class, 'showLogin'])->name('user.login');
	Route::post('/login', [App\Http\Controllers\User\AuthController::class, 'login'])->name('user.login.post');
	Route::get('/register', [App\Http\Controllers\User\AuthController::class, 'showRegister'])->name('user.register');
	Route::post('/register', [App\Http\Controllers\User\AuthController::class, 'register'])->name('user.register.post');
	Route::get('/forgot-password', [App\Http\Controllers\User\AuthController::class, 'showForgotPassword'])->name('user.forgot-password');
	Route::post('/forgot-password', [App\Http\Controllers\User\AuthController::class, 'forgotPassword'])->name('user.forgot-password.post');
	Route::match(['GET', 'POST'], '/logout', [App\Http\Controllers\User\AuthController::class, 'logout'])->name('user.logout');

	// Protected user routes
	Route::middleware('user.auth')->group(function () {
		// Dashboard
		Route::get('/', [App\Http\Controllers\User\DashboardController::class, 'index'])->name('user.dashboard');
		Route::get('/dashboard', [App\Http\Controllers\User\DashboardController::class, 'index'])->name('user.dashboard.alt');
		Route::get('/api/quick-stats', [App\Http\Controllers\User\DashboardController::class, 'getQuickStats'])->name('user.api.quick-stats');

		// Recharge routes
		Route::get('/recharge', [App\Http\Controllers\User\RechargeController::class, 'index'])->name('user.recharge');
		Route::post('/recharge/card', [App\Http\Controllers\User\RechargeController::class, 'cardRecharge'])->name('user.recharge.card');
		Route::post('/recharge/bank', [App\Http\Controllers\User\RechargeController::class, 'bankTransfer'])->name('user.recharge.bank');
		Route::get('/recharge/history', [App\Http\Controllers\User\RechargeController::class, 'history'])->name('user.recharge.history');
		Route::get('/recharge/{id}', [App\Http\Controllers\User\RechargeController::class, 'show'])->name('user.recharge.show');
		Route::post('/recharge/{id}/cancel', [App\Http\Controllers\User\RechargeController::class, 'cancel'])->name('user.recharge.cancel');

		// Withdraw routes
		Route::get('/withdraw', [App\Http\Controllers\User\WithdrawController::class, 'index'])->name('user.withdraw');
		Route::post('/withdraw', [App\Http\Controllers\User\WithdrawController::class, 'withdraw'])->name('user.withdraw.post');
		Route::get('/withdraw/history', [App\Http\Controllers\User\WithdrawController::class, 'history'])->name('user.withdraw.history');
		Route::get('/withdraw/{id}', [App\Http\Controllers\User\WithdrawController::class, 'show'])->name('user.withdraw.show');

		// Giftcode routes
		Route::get('/giftcode', [App\Http\Controllers\User\GiftcodeController::class, 'index'])->name('user.giftcode');
		Route::post('/giftcode/redeem', [App\Http\Controllers\User\GiftcodeController::class, 'redeem'])->name('user.giftcode.redeem');
		Route::get('/giftcode/history', [App\Http\Controllers\User\GiftcodeController::class, 'history'])->name('user.giftcode.history');
		Route::get('/giftcode/active', [App\Http\Controllers\User\GiftcodeController::class, 'getActiveGiftcodes'])->name('user.giftcode.active');
		Route::get('/giftcode/characters', [App\Http\Controllers\User\GiftcodeController::class, 'getCharacters'])->name('user.giftcode.characters');
		Route::get('/giftcode/active', [App\Http\Controllers\User\GiftcodeController::class, 'getActiveGiftcodes'])->name('user.giftcode.active');



		// Profile routes
		Route::get('/profile', [App\Http\Controllers\User\ProfileController::class, 'index'])->name('user.profile');
		Route::post('/profile/update', [App\Http\Controllers\User\ProfileController::class, 'update'])->name('user.profile.update');
		Route::post('/profile/change-password', [App\Http\Controllers\User\ProfileController::class, 'changePassword'])->name('user.profile.change-password');
		Route::get('/profile/stats', [App\Http\Controllers\User\ProfileController::class, 'getStats'])->name('user.profile.stats');

		// Monthly Card routes
		Route::get('/monthly-card', [App\Http\Controllers\User\MonthlyCardController::class, 'index'])->name('user.monthly-card');
		Route::get('/monthly-card/get-characters', [App\Http\Controllers\User\MonthlyCardController::class, 'getCharacters'])->name('user.monthly-card.get-characters');
		Route::post('/monthly-card/purchase', [App\Http\Controllers\User\MonthlyCardController::class, 'purchase'])->name('user.monthly-card.purchase');
		Route::post('/monthly-card/claim-daily', [App\Http\Controllers\User\MonthlyCardController::class, 'claimDaily'])->name('user.monthly-card.claim-daily');
		Route::post('/monthly-card/{id}/cancel', [App\Http\Controllers\User\MonthlyCardController::class, 'cancelOwn'])->name('user.monthly-card.cancel');


	});
});

// Admin Authentication Routes (no middleware)
Route::get('/admin/login', [App\Http\Controllers\Admin\AuthController::class, 'showLoginForm'])->name('admin.login');
Route::post('/admin/login', [App\Http\Controllers\Admin\AuthController::class, 'login'])->name('admin.login.post');
Route::match(['GET', 'POST'], '/admin/logout', [App\Http\Controllers\Admin\AuthController::class, 'logout'])->name('admin.logout');

// Admin Protected Routes (with middleware)
Route::group(['middleware' => ['web', 'admin', 'track.admin.activity']], function () {
	// Dashboard
	Route::get('/admin/dashboard', [App\Http\Controllers\Admin\AuthController::class, 'dashboard'])->name('admin.dashboard');

	// Admin User Management Routes
	Route::get('/admin/admin-users', [App\Http\Controllers\Admin\AdminUserController::class, 'index'])->name('admin.admin-users.index');
	Route::get('/admin/admin-users/create', [App\Http\Controllers\Admin\AdminUserController::class, 'create'])->name('admin.admin-users.create');
	Route::post('/admin/admin-users', [App\Http\Controllers\Admin\AdminUserController::class, 'store'])->name('admin.admin-users.store');
	Route::get('/admin/admin-users/{id}', [App\Http\Controllers\Admin\AdminUserController::class, 'show'])->name('admin.admin-users.show');
	Route::get('/admin/admin-users/{id}/edit', [App\Http\Controllers\Admin\AdminUserController::class, 'edit'])->name('admin.admin-users.edit');
	Route::post('/admin/admin-users/{id}/update', [App\Http\Controllers\Admin\AdminUserController::class, 'update'])->name('admin.admin-users.update');
	Route::post('/admin/admin-users/{id}/toggle-status', [App\Http\Controllers\Admin\AdminUserController::class, 'toggleStatus'])->name('admin.admin-users.toggle-status');

	// Admin Account Management Routes
	Route::get('/admin/accounts', [App\Http\Controllers\Admin\AccountController::class, 'index'])->name('admin.accounts.index');
	Route::get('/admin/accounts/test-connections', [App\Http\Controllers\Admin\AccountController::class, 'testConnections'])->name('admin.accounts.test-connections');
	Route::post('/admin/cache/clear-game', [App\Http\Controllers\Admin\AccountController::class, 'clearGameCache'])->name('admin.cache.clear-game');
	Route::get('/admin/accounts/{id}', [App\Http\Controllers\Admin\AccountController::class, 'show'])->name('admin.accounts.show');
	Route::get('/admin/accounts/{id}/edit', [App\Http\Controllers\Admin\AccountController::class, 'edit'])->name('admin.accounts.edit');
	Route::post('/admin/accounts/{id}/update', [App\Http\Controllers\Admin\AccountController::class, 'update'])->name('admin.accounts.update');
	Route::post('/admin/accounts/{id}/ban', [App\Http\Controllers\Admin\AccountController::class, 'ban'])->name('admin.accounts.ban');
	Route::post('/admin/accounts/{id}/unban', [App\Http\Controllers\Admin\AccountController::class, 'unban'])->name('admin.accounts.unban');

	// Game Money Management Routes
	Route::get('/admin/game-money', [App\Http\Controllers\Admin\GameMoneyController::class, 'index'])->name('admin.game-money.index');
	Route::get('/admin/game-money/{id}', [App\Http\Controllers\Admin\GameMoneyController::class, 'show'])->name('admin.game-money.show');
	Route::get('/admin/game-money/{id}/edit', [App\Http\Controllers\Admin\GameMoneyController::class, 'edit'])->name('admin.game-money.edit');
	Route::post('/admin/game-money/{id}/update', [App\Http\Controllers\Admin\GameMoneyController::class, 'update'])->name('admin.game-money.update');

	// Admin Character Management Routes
	Route::get('/admin/characters', [App\Http\Controllers\Admin\CharacterController::class, 'index'])->name('admin.characters.index');
	Route::get('/admin/characters/{id}', [App\Http\Controllers\Admin\CharacterController::class, 'show'])->name('admin.characters.show');
	Route::get('/admin/characters/{id}/edit', [App\Http\Controllers\Admin\CharacterController::class, 'edit'])->name('admin.characters.edit');
	Route::post('/admin/characters/{id}/update', [App\Http\Controllers\Admin\CharacterController::class, 'update'])->name('admin.characters.update');
	Route::post('/admin/characters/{id}/ban', [App\Http\Controllers\Admin\CharacterController::class, 'ban'])->name('admin.characters.ban');
	Route::post('/admin/characters/{id}/unban', [App\Http\Controllers\Admin\CharacterController::class, 'unban'])->name('admin.characters.unban');
	Route::delete('/admin/characters/{id}', [App\Http\Controllers\Admin\CharacterController::class, 'destroy'])->name('admin.characters.destroy');

	// Admin Coin Recharge Management Routes
	Route::get('/admin/coin-recharge', [App\Http\Controllers\Admin\CoinRechargeController::class, 'index'])->name('admin.coin-recharge.index');
	Route::get('/admin/coin-recharge/create', [App\Http\Controllers\Admin\CoinRechargeController::class, 'create'])->name('admin.coin-recharge.create');
	Route::get('/admin/coin-recharge/search-account', [App\Http\Controllers\Admin\CoinRechargeController::class, 'searchAccount'])->name('admin.coin-recharge.search-account');
	Route::get('/admin/coin-recharge/statistics', [App\Http\Controllers\Admin\CoinRechargeController::class, 'getStatistics'])->name('admin.coin-recharge.statistics');
	Route::get('/admin/coin-recharge/revenue-report', [App\Http\Controllers\Admin\CoinRechargeController::class, 'revenueReport'])->name('admin.coin-recharge.revenue-report');

	// Bulk coin recharge routes (must be before {id} route)
	Route::get('/admin/coin-recharge/bulk', [App\Http\Controllers\Admin\CoinRechargeController::class, 'bulkForm'])->name('admin.coin-recharge.bulk-form');
	Route::post('/admin/coin-recharge/bulk', [App\Http\Controllers\Admin\CoinRechargeController::class, 'bulkRecharge'])->name('admin.coin-recharge.bulk');
	Route::post('/admin/coin-recharge/bulk-preview', [App\Http\Controllers\Admin\CoinRechargeController::class, 'bulkPreview'])->name('admin.coin-recharge.bulk-preview');

	// Coin deduction routes
	Route::get('/admin/coin-deduct', [App\Http\Controllers\Admin\CoinRechargeController::class, 'deductForm'])->name('admin.coin-recharge.deduct-form');
	Route::post('/admin/coin-deduct', [App\Http\Controllers\Admin\CoinRechargeController::class, 'deduct'])->name('admin.coin-recharge.deduct');

	// Individual transaction routes (must be after specific routes)
	Route::post('/admin/coin-recharge', [App\Http\Controllers\Admin\CoinRechargeController::class, 'store'])->name('admin.coin-recharge.store');
	Route::get('/admin/coin-recharge/{id}', [App\Http\Controllers\Admin\CoinRechargeController::class, 'show'])->name('admin.coin-recharge.show');

	// Get characters API for admin forms
	Route::get('/admin/coin-recharge/get-characters', [App\Http\Controllers\Admin\CoinRechargeController::class, 'getCharacters'])->name('admin.coin-recharge.get-characters');

	// Admin Giftcode Management Routes
	Route::get('/admin/giftcode', [App\Http\Controllers\Admin\GiftcodeController::class, 'index'])->name('admin.giftcode.index');
	Route::get('/admin/giftcode/create', [App\Http\Controllers\Admin\GiftcodeController::class, 'create'])->name('admin.giftcode.create');
	Route::post('/admin/giftcode', [App\Http\Controllers\Admin\GiftcodeController::class, 'store'])->name('admin.giftcode.store');
	Route::get('/admin/giftcode/{id}', [App\Http\Controllers\Admin\GiftcodeController::class, 'show'])->name('admin.giftcode.show');
	Route::get('/admin/giftcode/{id}/edit', [App\Http\Controllers\Admin\GiftcodeController::class, 'edit'])->name('admin.giftcode.edit');
	Route::put('/admin/giftcode/{id}', [App\Http\Controllers\Admin\GiftcodeController::class, 'update'])->name('admin.giftcode.update');
	Route::delete('/admin/giftcode/{id}', [App\Http\Controllers\Admin\GiftcodeController::class, 'destroy'])->name('admin.giftcode.destroy');
	Route::post('/admin/giftcode/{id}/toggle-status', [App\Http\Controllers\Admin\GiftcodeController::class, 'toggleStatus'])->name('admin.giftcode.toggle-status');
	Route::get('/admin/giftcode/usage-report', [App\Http\Controllers\Admin\GiftcodeController::class, 'usageReport'])->name('admin.giftcode.usage-report');
	Route::get('/admin/giftcode/api/items', [App\Http\Controllers\Admin\GiftcodeController::class, 'getItems'])->name('admin.giftcode.api.items');



	// Admin Analytics Routes
	Route::get('/admin/analytics', [App\Http\Controllers\Admin\AnalyticsController::class, 'index'])->name('admin.analytics.index');
	Route::get('/admin/analytics/export', [App\Http\Controllers\Admin\AnalyticsController::class, 'export'])->name('admin.analytics.export');
	Route::post('/admin/analytics/clear-cache', [App\Http\Controllers\Admin\AnalyticsController::class, 'clearCache'])->name('admin.analytics.clear-cache');
	Route::get('/admin/analytics/ajax-data', [App\Http\Controllers\Admin\AnalyticsController::class, 'ajaxData'])->name('admin.analytics.ajax-data');

	// Admin IP Management Routes
	Route::get('/admin/ip-management', [App\Http\Controllers\Admin\IpManagementController::class, 'index'])->name('admin.ip-management.index');
	Route::get('/admin/ip-management/banned', [App\Http\Controllers\Admin\IpManagementController::class, 'bannedIps'])->name('admin.ip-management.banned');
	Route::get('/admin/ip-management/suspicious', [App\Http\Controllers\Admin\IpManagementController::class, 'suspicious'])->name('admin.ip-management.suspicious');
	Route::get('/admin/ip-management/{ip}', [App\Http\Controllers\Admin\IpManagementController::class, 'show'])->name('admin.ip-management.show');
	Route::post('/admin/ip-management/{ip}/ban', [App\Http\Controllers\Admin\IpManagementController::class, 'banIp'])->name('admin.ip-management.ban');
	Route::post('/admin/ip-management/{ip}/unban', [App\Http\Controllers\Admin\IpManagementController::class, 'unbanIp'])->name('admin.ip-management.unban');
	Route::get('/admin/ip-management/export', [App\Http\Controllers\Admin\IpManagementController::class, 'export'])->name('admin.ip-management.export');

	// Admin System Routes
	Route::get('/admin/system/performance', [App\Http\Controllers\Admin\SystemController::class, 'performance'])->name('admin.system.performance');
	Route::post('/admin/system/clear-cache', [App\Http\Controllers\Admin\SystemController::class, 'clearCache'])->name('admin.system.clear-cache');
	Route::get('/admin/system/logs', [App\Http\Controllers\Admin\SystemController::class, 'logs'])->name('admin.system.logs');

	// Admin Logs Routes (Legacy)
	Route::get('/admin/logs', [App\Http\Controllers\Admin\AdminLogsController::class, 'index'])->name('admin.logs.index');
	Route::get('/admin/logs/{id}', [App\Http\Controllers\Admin\AdminLogsController::class, 'show'])->name('admin.logs.show');
	Route::get('/admin/logs/export', [App\Http\Controllers\Admin\AdminLogsController::class, 'export'])->name('admin.logs.export');
	Route::get('/admin/logs/statistics', [App\Http\Controllers\Admin\AdminLogsController::class, 'statistics'])->name('admin.logs.statistics');
	Route::get('/admin/logs/login-logs', [App\Http\Controllers\Admin\AdminLogsController::class, 'loginLogs'])->name('admin.logs.login-logs');

	// Card Month Management routes (integrated from old system)
	Route::get('/admin/card-month', [App\Http\Controllers\Admin\CardMonthController::class, 'index'])->name('admin.card-month.index');
	Route::post('/admin/card-month/create', [App\Http\Controllers\Admin\CardMonthController::class, 'create'])->name('admin.card-month.create');
	Route::post('/admin/card-month/{id}/cancel', [App\Http\Controllers\Admin\CardMonthController::class, 'cancel'])->name('admin.card-month.cancel');
	Route::post('/admin/card-month/{id}/extend', [App\Http\Controllers\Admin\CardMonthController::class, 'extend'])->name('admin.card-month.extend');
	Route::get('/admin/card-month/search-account', [App\Http\Controllers\Admin\CardMonthController::class, 'searchAccount'])->name('admin.card-month.search-account');

	// Admin Activity Management
	Route::get('/admin/activity/dashboard', [App\Http\Controllers\Admin\AdminActivityController::class, 'dashboard'])->name('admin.activity.dashboard');
	Route::get('/admin/activity/online-admins', [App\Http\Controllers\Admin\AdminActivityController::class, 'getOnlineAdmins'])->name('admin.activity.online-admins');
	Route::post('/admin/activity/update-activity', [App\Http\Controllers\Admin\AdminActivityController::class, 'updateActivity'])->name('admin.activity.update-activity');
	Route::post('/admin/activity/check-conflict', [App\Http\Controllers\Admin\AdminActivityController::class, 'checkConflict'])->name('admin.activity.check-conflict');
	Route::post('/admin/activity/create-lock', [App\Http\Controllers\Admin\AdminActivityController::class, 'createLock'])->name('admin.activity.create-lock');
	Route::post('/admin/activity/release-lock', [App\Http\Controllers\Admin\AdminActivityController::class, 'releaseLock'])->name('admin.activity.release-lock');
	Route::get('/admin/activity/conflicts', [App\Http\Controllers\Admin\AdminActivityController::class, 'getConflicts'])->name('admin.activity.conflicts');
	Route::post('/admin/activity/resolve-conflict/{id}', [App\Http\Controllers\Admin\AdminActivityController::class, 'resolveConflict'])->name('admin.activity.resolve-conflict');
	Route::post('/admin/activity/force-unlock', [App\Http\Controllers\Admin\AdminActivityController::class, 'forceUnlock'])->name('admin.activity.force-unlock');

	// Admin Battle Pass Routes
	Route::get('/admin/battle-pass', [App\Http\Controllers\Admin\BattlePassController::class, 'index'])->name('admin.battle-pass.index');
	Route::get('/admin/battle-pass/create', [App\Http\Controllers\Admin\BattlePassController::class, 'create'])->name('admin.battle-pass.create');
	Route::post('/admin/battle-pass', [App\Http\Controllers\Admin\BattlePassController::class, 'store'])->name('admin.battle-pass.store');
	Route::get('/admin/battle-pass/{id}', [App\Http\Controllers\Admin\BattlePassController::class, 'show'])->name('admin.battle-pass.show');
	Route::post('/admin/battle-pass/{id}/extend', [App\Http\Controllers\Admin\BattlePassController::class, 'extend'])->name('admin.battle-pass.extend');
	Route::post('/admin/battle-pass/{id}/cancel', [App\Http\Controllers\Admin\BattlePassController::class, 'cancel'])->name('admin.battle-pass.cancel');
	Route::get('/admin/battle-pass/search-account', [App\Http\Controllers\Admin\BattlePassController::class, 'searchAccount'])->name('admin.battle-pass.search-account');
});

// Redirect /admin to login page
Route::get('/admin', function () {
	// Check if user is logged in
	if (Session::has('admin_user')) {
		return redirect('/admin/dashboard');
	}
	return redirect('/admin/login');
});



// Old UserCP routes removed - functionality moved to new User namespace controllers

// Old AdminCP routes removed - functionality moved to new Admin namespace controllers

// Old API routes moved to api.php file
