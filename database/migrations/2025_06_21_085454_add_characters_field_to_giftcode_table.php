<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddCharactersFieldToGiftcodeTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('t_giftcode', function (Blueprint $table) {
            $table->text('characters')->nullable()->after('accounts')->comment('Comma-separated character names for character-specific codes');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('t_giftcode', function (Blueprint $table) {
            $table->dropColumn('characters');
        });
    }
}
